import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import '../../request/dop_native/dop_native_application_submit_form_request.dart';
import '../../request/dop_native/dop_native_collect_location_request.dart';
import '../../request/dop_native/dop_native_esign_form_request.dart';
import '../../request/dop_native/dop_native_log_event_data_request.dart';
import '../../response/dop_native/dop_native_activate_card_entity.dart';
import '../../response/dop_native/dop_native_application_form_data_entity.dart';
import '../../response/dop_native/dop_native_application_state_entity.dart';
import '../../response/dop_native/dop_native_appraising_status_entity.dart';
import '../../response/dop_native/dop_native_bootstrap_auth_settings_entity.dart';
import '../../response/dop_native/dop_native_bootstrap_entity.dart';
import '../../response/dop_native/dop_native_card_status_entity.dart';
import '../../response/dop_native/dop_native_credit_assignment_entity.dart';
import '../../response/dop_native/dop_native_esign_prepare_entity.dart';
import '../../response/dop_native/dop_native_metadata_entity.dart';
import '../../response/dop_native/dop_native_metadata_suggestion_entity.dart';
import '../../response/dop_native/dop_native_personalize_reward_entity.dart';
import '../../response/dop_native/dop_native_register_entity.dart';
import '../../response/dop_native/dop_native_registration_campaign_entity.dart';
import '../../response/dop_native/dop_native_request_otp_entity.dart';
import '../../response/dop_native/dop_verify_otp_entity.dart';
import '../../response/dop_native/e_sign_state_entity.dart';

abstract class DOPNativeRepo {
  Future<DOPNativeRegistrationCampaignEntity> registrationCampaign({
    String? campaignCode,
    String leadSource = LeadSource.evoNative,
    MockConfig? mockConfig,
  });

  Future<DOPNativeRegisterEntity> register({
    required String phoneNumber,
    required String campaignCode,
    required String source,
    required String signature,
    required String platform,
    Map<String, dynamic>? collectedData,
    Map<String, dynamic>? params,
    bool allowedSwitchFlow = false,
    MockConfig? mockConfig,
  });

  Future<DOPNativeRequestOTPEntity> requestOTP({
    required String? token,
    MockConfig? mockConfig,
  });

  Future<DOPNativeBootstrapEntity> getBootstrapInfo({
    required String? token,
    MockConfig? mockConfig,
  });

  Future<DOPNativeBootstrapAuthSettingsEntity> getBootstrapAuthSettings({
    required String? token,
    MockConfig? mockConfig,
  });

  Future<DOPVerifyOTPEntity> verifyOTP({
    required String? token,
    required String? otp,
    MockConfig? mockConfig,
  });

  /// To get exactly current_step, ui_version and flow_selected_at to redirect to the correct screen, Mobile must call 2 APIs:
  /// 1. Get /api/application/state
  /// - This API will return the current_step.
  /// - Refer to: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Application-State-API
  /// 2. Get /api/bootstrap
  /// - This API will return the ui_version and flow_selected_at.
  /// - Refer to: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#GET-bootstrap
  ///
  /// To reduce the number of API calls, EVO BE supports to combine these 2 APIs into 1 API.
  /// Refer to API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3544678490/ES+Combine+API+get+application+state+and+get+bootstrap
  Future<DOPNativeApplicationStateEntity> getApplicationState({
    String? token,
    int? flowSelectedAt,
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Metadata-Data-API
  Future<DOPNativeMetadataEntity> getMetadata({
    required MetadataType type,
    String? parentCode,
    MetadataType? parentType,
    String? metadataCode,
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Metadata-Data-Suggestion-API
  Future<DOPNativeMetadataSuggestionEntity> getMetadataSuggestion({
    required MetadataSuggestionType type,
    required String searchPrefix,
    int? pageSize,
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#GET-Appraising-status
  Future<DOPNativeAppraisingStatusEntity> getAppraisingStatus({
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#GET-Credit-assignment
  Future<DOPNativeCreditAssignmentEntity> getAppraisingCreditAssignment({
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Form-Data-API
  Future<DOPNativeApplicationFormDataEntity> getApplicationFormData({
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3565223937/CIF+Flow#In-state-cif.confirm
  Future<BaseEntity> confirmCif({
    required bool useNewCif,
    MockConfig? mockConfig,
  });

  /// https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#POST-Application-next-state
  Future<BaseEntity> getApplicationNextState({
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Application-Submit-Form-Data-API
  Future<BaseEntity> submitApplicationForm({
    required DOPNativeApplicationSubmitFormRequest form,
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3580264451/E-Success#GET-esign-state
  Future<ESignStateEntity> getESignState({
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3580264451/E-Success#GET-Card-status
  Future<DOPNativeCardStatusEntity> getCardStatus({
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3583082497/Underwriting+sub+flow+Card+activation#POST-Activate-card
  Future<DOPNativeActivateCardEntity> activateCard({
    required int posLimit,
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3544449025/S7+KH+k+h+p+ng+i+n+t+v+x+c+nh+n+tu+n+th+FATCA#Submit-Esign
  Future<BaseEntity> submitESign({
    required DOPNativeESignFormRequest form,
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3544449025/S7+KH+k+h+p+ng+i+n+t+v+x+c+nh+n+tu+n+th+FATCA#Prepare-Esign
  Future<DOPNativeESignPrepareEntity> prepareESign({
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3565125633/State+Esign+Intro
  Future<BaseEntity> submitESignIntroNext({
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3546251466
  Future<BaseEntity> logEvent({
    required DOPNativeLogEventDataRequest data,
    MockConfig? mockConfig,
  });

  /// API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Collect-Extra-Data
  Future<BaseEntity> collectLocation({
    required DOPNativeCollectLocationRequest data,
    MockConfig? mockConfig,
  });

  /// API Spec:https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/4005626238/CO-12350+Flow+for+New+users+onboarding+at+Partner+Offline+stores
  Future<DOEPersonalizeRewardEntity> getPersonalizeReward({
    MockConfig? mockConfig,
  });
}
