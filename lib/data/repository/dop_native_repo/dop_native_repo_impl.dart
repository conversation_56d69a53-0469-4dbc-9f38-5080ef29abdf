import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../request/dop_native/dop_native_application_submit_form_request.dart';
import '../../request/dop_native/dop_native_collect_location_request.dart';
import '../../request/dop_native/dop_native_esign_form_request.dart';
import '../../request/dop_native/dop_native_log_event_data_request.dart';
import '../../response/dop_native/dop_native_activate_card_entity.dart';
import '../../response/dop_native/dop_native_application_form_data_entity.dart';
import '../../response/dop_native/dop_native_application_state_entity.dart';
import '../../response/dop_native/dop_native_appraising_status_entity.dart';
import '../../response/dop_native/dop_native_bootstrap_auth_settings_entity.dart';
import '../../response/dop_native/dop_native_bootstrap_entity.dart';
import '../../response/dop_native/dop_native_card_status_entity.dart';
import '../../response/dop_native/dop_native_credit_assignment_entity.dart';
import '../../response/dop_native/dop_native_esign_prepare_entity.dart';
import '../../response/dop_native/dop_native_metadata_entity.dart';
import '../../response/dop_native/dop_native_metadata_suggestion_entity.dart';
import '../../response/dop_native/dop_native_personalize_reward_entity.dart';
import '../../response/dop_native/dop_native_register_entity.dart';
import '../../response/dop_native/dop_native_registration_campaign_entity.dart';
import '../../response/dop_native/dop_native_request_otp_entity.dart';
import '../../response/dop_native/dop_verify_otp_entity.dart';
import '../../response/dop_native/e_sign_state_entity.dart';
import '../base_repo.dart';
import 'dop_native_repo.dart';

class DOPNativeRepoImpl extends BaseRepo implements DOPNativeRepo {
  static const String dopNativeBaseUrl = 'dop/api';

  static const String baseOTPURL = '$dopNativeBaseUrl/otp';
  static const String baseBootstrapURL = '$dopNativeBaseUrl/bootstrap';

  static const String campaignUrl = '$dopNativeBaseUrl/registration/campaign';
  static const String registerUrl = '$dopNativeBaseUrl/registration/register';

  /// OTP
  static const String requestOTPURL = '$baseOTPURL/send';
  static const String verifyOTPURL = '$baseOTPURL/verify';

  /// Bootstrap
  static const String getBootstrapAuthSettingsURL = '$baseBootstrapURL/auth_settings';

  /// Application State
  static const String getApplicationStateURL = '/dop/application/state';

  /// Metadata
  static const String metadataURL = '$dopNativeBaseUrl/metadata';

  /// Metadata Suggestion
  static const String metadataSuggestionURL = '$dopNativeBaseUrl/metadata/suggest';

  /// Appraising Status
  static const String appraisingStatusURL = '$dopNativeBaseUrl/appraising/status';

  /// Credit assignment
  static const String appraisingCreditAssignment = '$dopNativeBaseUrl/appraising/credit_assignment';

  /// CIF confirm
  static const String cifConfirmUrl = '$dopNativeBaseUrl/cif/confirm';

  /// Form Data
  static const String applicationFormDataURL = '$dopNativeBaseUrl/application/form_data';

  /// Application next state
  static const String getApplicationNextStateUrl = '$dopNativeBaseUrl/application/state/next';

  /// Submit form application
  static const String submitFormApplicationUrl = '$dopNativeBaseUrl/application/form/submit';

  /// eSign
  static const String getESignStateUrl = '$dopNativeBaseUrl/esign/state';
  static const String submitESignIntroNextUrl = '$dopNativeBaseUrl/esign/intro/next';

  /// Get card status
  static const String cardStatusUrl = '$dopNativeBaseUrl/card/status';

  /// Activate card
  static const String activateCardUrl = '$dopNativeBaseUrl/card/activate';

  /// Submit e-sign form
  static const String submitESignUrl = '$dopNativeBaseUrl/esign/form/submit';

  /// Prepare e-sign
  static const String eSignPrepareUrl = '$dopNativeBaseUrl/esign/prepare';

  /// Log event
  static const String logEventUrl = '$dopNativeBaseUrl/event/track';

  /// Collect Location
  static const String collectLocationUrl = '$dopNativeBaseUrl/collect_extra/location';

  static const String personalizeRewardMetadataUrl =
      '$dopNativeBaseUrl/personalize_reward/metadata';

  DOPNativeRepoImpl(super.client);

  @override
  Future<DOPNativeRegistrationCampaignEntity> registrationCampaign({
    String? campaignCode,
    String leadSource = LeadSource.evoNative,
    MockConfig? mockConfig,
  }) async {
    final Map<String, String> params = <String, String>{
      'partner': leadSource,
    };

    if (campaignCode != null) {
      params['campaign_code'] = campaignCode;
    }

    final BaseResponse baseResponse = await client.get(
      campaignUrl,
      params: params,
      mockConfig: mockConfig,
    );

    final DOPNativeRegistrationCampaignEntity registrationCampaignEntity = commonUtilFunction
            .serialize(() => DOPNativeRegistrationCampaignEntity.fromBaseResponse(baseResponse),
                originalData: baseResponse) ??
        DOPNativeRegistrationCampaignEntity.unserializable();
    return registrationCampaignEntity;
  }

  @override
  Future<DOPNativeRegisterEntity> register({
    required String phoneNumber,
    required String campaignCode,
    required String source,
    required String signature,
    required String platform,
    Map<String, dynamic>? collectedData,
    Map<String, dynamic>? params,
    bool allowedSwitchFlow = false,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> body = <String, dynamic>{
      'phone_number': phoneNumber,
      'campaign_code': campaignCode,
      'source': source,
      'allowed_switch_flow': allowedSwitchFlow,
    };

    if (collectedData != null) {
      body['collected_data'] = collectedData;
    }

    if (params != null) {
      body['params'] = params;
    }

    body['signature'] = signature;
    body['platform'] = platform;

    final BaseResponse baseResponse = await client.post(
      registerUrl,
      data: body,
      mockConfig: mockConfig,
    );

    final DOPNativeRegisterEntity registerEntity = commonUtilFunction.serialize(
            () => DOPNativeRegisterEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        DOPNativeRegisterEntity.unserializable();
    return registerEntity;
  }

  @override
  Future<DOPNativeRequestOTPEntity> requestOTP({
    required String? token,
    MockConfig? mockConfig,
  }) async {
    final BaseResponse baseResponse = await client.post(
      requestOTPURL,
      data: <String, dynamic>{
        'token': token,
      },
      mockConfig: mockConfig,
    );

    final DOPNativeRequestOTPEntity entity = commonUtilFunction.serialize(
          () => DOPNativeRequestOTPEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        DOPNativeRequestOTPEntity.unserializable();
    return entity;
  }

  @override
  Future<DOPNativeBootstrapEntity> getBootstrapInfo({
    required String? token,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> params = <String, dynamic>{
      'token': token,
    };

    final BaseResponse baseResponse = await client.get(
      baseBootstrapURL,
      params: params,
      mockConfig: mockConfig,
    );

    final DOPNativeBootstrapEntity entity = commonUtilFunction.serialize(
          () => DOPNativeBootstrapEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        DOPNativeBootstrapEntity.unserializable();
    return entity;
  }

  @override
  Future<DOPNativeBootstrapAuthSettingsEntity> getBootstrapAuthSettings({
    required String? token,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> params = <String, dynamic>{
      'token': token,
    };

    final BaseResponse baseResponse = await client.get(
      getBootstrapAuthSettingsURL,
      params: params,
      mockConfig: mockConfig,
    );

    final DOPNativeBootstrapAuthSettingsEntity entity = commonUtilFunction.serialize(
          () => DOPNativeBootstrapAuthSettingsEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        DOPNativeBootstrapAuthSettingsEntity.unserializable();
    return entity;
  }

  @override
  Future<DOPVerifyOTPEntity> verifyOTP({
    required String? token,
    required String? otp,
    MockConfig? mockConfig,
  }) async {
    final BaseResponse baseResponse = await client.post(
      verifyOTPURL,
      data: <String, dynamic>{
        'token': token,
        'otp': otp,
      },
      mockConfig: mockConfig,
    );

    final DOPVerifyOTPEntity entity = commonUtilFunction.serialize(
          () => DOPVerifyOTPEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        DOPVerifyOTPEntity.unserializable();
    return entity;
  }

  @override
  Future<DOPNativeApplicationStateEntity> getApplicationState({
    String? token,
    int? flowSelectedAt,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> params = <String, dynamic>{
      'token': token,
    };

    if (flowSelectedAt != null) {
      params['flow_selected_at'] = flowSelectedAt;
    }

    final BaseResponse baseResponse = await client.get(
      getApplicationStateURL,
      params: params,
      mockConfig: mockConfig,
    );

    final DOPNativeApplicationStateEntity entity = commonUtilFunction.serialize(
          () => DOPNativeApplicationStateEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        DOPNativeApplicationStateEntity.unserializable();

    return entity;
  }

  @override
  Future<DOPNativeMetadataEntity> getMetadata({
    required MetadataType type,
    String? parentCode,
    MetadataType? parentType,
    String? metadataCode,
    MockConfig? mockConfig,
  }) async {
    final Map<String, String> params = <String, String>{
      'type': type.value,
    };

    if (parentCode != null) {
      params['parent_code'] = parentCode;
    }

    if (parentType != null) {
      params['parent_type'] = parentType.value;
    }

    if (metadataCode != null) {
      params['metadata_code'] = metadataCode;
    }

    final BaseResponse baseResponse = await client.get(
      metadataURL,
      params: params,
      mockConfig: mockConfig,
    );

    final DOPNativeMetadataEntity entity = commonUtilFunction.serialize(
            () => DOPNativeMetadataEntity.fromJson(baseResponse),
            originalData: baseResponse) ??
        DOPNativeMetadataEntity.unserializable();

    return entity;
  }

  @override
  Future<DOPNativeMetadataSuggestionEntity> getMetadataSuggestion({
    required MetadataSuggestionType type,
    required String searchPrefix,
    int? pageSize,
    MockConfig? mockConfig,
  }) async {
    final Map<String, String> params = <String, String>{
      'type': type.value,
      'search_prefix': searchPrefix,
    };

    if (pageSize != null) {
      params['page_size'] = pageSize.toString();
    }

    final BaseResponse baseResponse = await client.get(
      metadataSuggestionURL,
      params: params,
      mockConfig: mockConfig,
    );

    final DOPNativeMetadataSuggestionEntity entity = commonUtilFunction.serialize(
            () => DOPNativeMetadataSuggestionEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        DOPNativeMetadataSuggestionEntity.unserializable();

    return entity;
  }

  @override
  Future<DOPNativeAppraisingStatusEntity> getAppraisingStatus({MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.get(
      appraisingStatusURL,
      mockConfig: mockConfig,
    );

    final DOPNativeAppraisingStatusEntity entity = commonUtilFunction.serialize(
            () => DOPNativeAppraisingStatusEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        DOPNativeAppraisingStatusEntity.unserializable();

    return entity;
  }

  @override
  Future<DOPNativeCreditAssignmentEntity> getAppraisingCreditAssignment(
      {MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.get(
      appraisingCreditAssignment,
      mockConfig: mockConfig,
    );

    final DOPNativeCreditAssignmentEntity entity = commonUtilFunction.serialize(
            () => DOPNativeCreditAssignmentEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        DOPNativeCreditAssignmentEntity.unserializable();

    return entity;
  }

  @override
  Future<BaseEntity> submitApplicationForm({
    required DOPNativeApplicationSubmitFormRequest form,
    MockConfig? mockConfig,
  }) async {
    final BaseResponse baseResponse = await client.post(
      submitFormApplicationUrl,
      data: form.toJson(),
      mockConfig: mockConfig,
    );

    return BaseEntity.fromBaseResponse(baseResponse);
  }

  @override
  Future<DOPNativeApplicationFormDataEntity> getApplicationFormData(
      {MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.get(
      applicationFormDataURL,
      mockConfig: mockConfig,
    );

    final DOPNativeApplicationFormDataEntity entity = commonUtilFunction.serialize(
          () => DOPNativeApplicationFormDataEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        DOPNativeApplicationFormDataEntity.unserializable();

    return entity;
  }

  @override
  Future<BaseEntity> confirmCif({required bool useNewCif, MockConfig? mockConfig}) async {
    final Map<String, dynamic> body = <String, dynamic>{
      'use_new_cif': useNewCif,
    };

    final BaseResponse baseResponse = await client.post(
      cifConfirmUrl,
      data: body,
      mockConfig: mockConfig,
    );

    return BaseEntity.fromBaseResponse(baseResponse);
  }

  @override
  Future<BaseEntity> getApplicationNextState({MockConfig? mockConfig}) async {
    final Map<String, dynamic> body = <String, dynamic>{
      'action': 'evo_next',
    };

    final BaseResponse baseResponse = await client.post(
      getApplicationNextStateUrl,
      data: body,
      mockConfig: mockConfig,
    );

    return BaseEntity.fromBaseResponse(baseResponse);
  }

  @override
  Future<ESignStateEntity> getESignState({MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.get(
      getESignStateUrl,
      mockConfig: mockConfig,
    );

    final ESignStateEntity entity = commonUtilFunction.serialize(
          () => ESignStateEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        ESignStateEntity.unserializable();

    return entity;
  }

  @override
  Future<DOPNativeCardStatusEntity> getCardStatus({MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.get(
      cardStatusUrl,
      mockConfig: mockConfig,
    );

    return commonUtilFunction.serialize(
            () => DOPNativeCardStatusEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        DOPNativeCardStatusEntity.unserializable();
  }

  @override
  Future<DOPNativeActivateCardEntity> activateCard({
    required int posLimit,
    MockConfig? mockConfig,
  }) async {
    final BaseResponse baseResponse = await client.post(
      activateCardUrl,
      data: <String, dynamic>{
        'pos_limit': posLimit,
      },
      mockConfig: mockConfig,
    );

    return commonUtilFunction.serialize(
            () => DOPNativeActivateCardEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        DOPNativeActivateCardEntity.unserializable();
  }

  @override
  Future<DOPNativeESignPrepareEntity> prepareESign({MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.post(
      eSignPrepareUrl,
      mockConfig: mockConfig,
    );

    return commonUtilFunction.serialize(
            () => DOPNativeESignPrepareEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        DOPNativeESignPrepareEntity.unserializable();
  }

  @override
  Future<BaseEntity> submitESign(
      {required DOPNativeESignFormRequest form, MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.post(
      submitESignUrl,
      data: form.toJson(),
      mockConfig: mockConfig,
    );

    return BaseEntity.fromBaseResponse(baseResponse);
  }

  @override
  Future<BaseEntity> submitESignIntroNext({MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.post(
      submitESignIntroNextUrl,
      mockConfig: mockConfig,
    );

    return BaseEntity.fromBaseResponse(baseResponse);
  }

  @override
  Future<BaseEntity> logEvent({
    required DOPNativeLogEventDataRequest data,
    MockConfig? mockConfig,
  }) async {
    final BaseResponse baseResponse = await client.post(
      logEventUrl,
      data: data.toJson(),
      mockConfig: mockConfig,
    );

    return BaseEntity.fromBaseResponse(baseResponse);
  }

  @override
  Future<BaseEntity> collectLocation({
    required DOPNativeCollectLocationRequest data,
    MockConfig? mockConfig,
  }) async {
    final BaseResponse baseResponse = await client.post(
      collectLocationUrl,
      data: data.toJson(),
      mockConfig: mockConfig,
    );

    return BaseEntity.fromBaseResponse(baseResponse);
  }

  @override
  Future<DOEPersonalizeRewardEntity> getPersonalizeReward({
    MockConfig? mockConfig,
  }) async {
    final BaseResponse baseResponse = await client.post(
      personalizeRewardMetadataUrl,
      mockConfig: mockConfig,
    );

    return commonUtilFunction.serialize(
            () => DOEPersonalizeRewardEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        DOEPersonalizeRewardEntity.unserializable();
  }
}
