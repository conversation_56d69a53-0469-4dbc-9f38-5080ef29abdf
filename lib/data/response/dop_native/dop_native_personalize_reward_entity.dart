// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class DOEPersonalizeRewardEntity extends BaseEntity {
  final DOEPersonalizeRewardHeader? header;
  final List<DOEPersonalizeRewardCategory>? category;
  final List<DOEPersonalizeRewardTnC>? tnc;

  DOEPersonalizeRewardEntity({
    this.header,
    this.category,
    this.tnc,
  });

  DOEPersonalizeRewardEntity.unserializable()
      : header = null,
        category = null,
        tnc = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  DOEPersonalizeRewardEntity.fromBaseResponse(BaseResponse super.response)
      : header = response.data?['header'] != null
            ? DOEPersonalizeRewardHeader.fromJson(response.data?['header'] as Map<String, dynamic>)
            : null,
        category = (response.data?['category'] as List<dynamic>?)
            ?.map((dynamic e) => DOEPersonalizeRewardCategory.fromJson(e as Map<String, dynamic>))
            .toList(),
        tnc = (response.data?['tnc'] as List<dynamic>?)
            ?.map((dynamic e) => DOEPersonalizeRewardTnC.fromJson(e as Map<String, dynamic>))
            .toList(),
        super.fromBaseResponse();
}

class DOEPersonalizeRewardHeader {
  final String? title;
  final String? desc;
  final int? waitingTime;
  final String? defaultName;

  const DOEPersonalizeRewardHeader({
    this.title,
    this.desc,
    this.waitingTime,
    this.defaultName,
  });

  factory DOEPersonalizeRewardHeader.fromJson(Map<String, dynamic> json) {
    return DOEPersonalizeRewardHeader(
      title: json['title'] as String?,
      desc: json['desc'] as String?,
      waitingTime: json['waiting_time'] as int?,
      defaultName: json['default_name'] as String?,
    );
  }
}

class DOEPersonalizeRewardCategoryDescription {
  final String? section;
  final List<String>? content;

  const DOEPersonalizeRewardCategoryDescription({
    this.section,
    this.content,
  });

  factory DOEPersonalizeRewardCategoryDescription.fromJson(Map<String, dynamic> json) {
    return DOEPersonalizeRewardCategoryDescription(
      section: json['section'] as String?,
      content: (json['content'] as List<dynamic>?)?.map((dynamic e) => e as String).toList(),
    );
  }
}

class DOEPersonalizeRewardCategory {
  final String? group;
  final String? code;
  final String? name;
  final List<String>? logo;
  final List<String>? shortDesc;
  final String? banner;
  final List<DOEPersonalizeRewardCategoryDescription>? desc;

  const DOEPersonalizeRewardCategory({
    this.group,
    this.code,
    this.name,
    this.logo,
    this.shortDesc,
    this.banner,
    this.desc,
  });

  factory DOEPersonalizeRewardCategory.fromJson(Map<String, dynamic> json) {
    return DOEPersonalizeRewardCategory(
      group: json['group'] as String?,
      code: json['code'] as String?,
      name: json['name'] as String?,
      logo: (json['logo'] as List<dynamic>?)?.map((dynamic e) => e as String).toList(),
      shortDesc: (json['short_desc'] as List<dynamic>?)?.map((dynamic e) => e as String).toList(),
      banner: json['banner'] as String?,
      desc: (json['desc'] as List<dynamic>?)
          ?.map((dynamic e) =>
              DOEPersonalizeRewardCategoryDescription.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class DOEPersonalizeRewardTnC {
  final String? name;
  final String? src;

  const DOEPersonalizeRewardTnC({
    this.name,
    this.src,
  });

  factory DOEPersonalizeRewardTnC.fromJson(Map<String, dynamic> json) {
    return DOEPersonalizeRewardTnC(
      name: json['name'] as String?,
      src: json['src'] as String?,
    );
  }
}
