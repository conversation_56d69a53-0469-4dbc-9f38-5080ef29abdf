// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../data/response/dop_native/dop_native_personalize_reward_entity.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../base/dop_native_page_state_base.dart';
import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import '../../util/metadata/dop_native_metadata_utils_impl.dart';
import '../../widgets/appbar/dop_native_appbar_widget.dart';
import 'cubit/dop_native_personalize_reward_cubit.dart';
import 'cubit/dop_native_personalize_reward_state.dart';
import 'widgets/dop_native_personalize_reward_list_widget.dart';

class DOPNativePersonalizeRewardScreen extends PageBase {
  final DOPNativePersonalizeRewardCubit? cubit;

  const DOPNativePersonalizeRewardScreen({
    this.cubit,
    super.key,
  });

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativePersonalizeRewardScreen.name,
    );
  }

  @override
  DOPNativePageStateBase<DOPNativePersonalizeRewardScreen> createState() =>
      DOPNativePersonalizeRewardScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativePersonalizeRewardScreen.routeName);
}

class DOPNativePersonalizeRewardScreenState
    extends DOPNativePageStateBase<DOPNativePersonalizeRewardScreen> {
  late final DOPNativePersonalizeRewardCubit cubit = widget.cubit ??
      DOPNativePersonalizeRewardCubit(
        appState: getIt<AppState>(),
        dopNativeRepo: getIt<DOPNativeRepo>(),
        metadataUtils: DOPNativeMetadataUtilsImpl(dopNativeRepo: getIt.get<DOPNativeRepo>()),
      );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      cubit.loadRewards();
    });
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: SafeArea(
        child: BlocProvider<DOPNativePersonalizeRewardCubit>(
          create: (_) => cubit,
          child: BlocConsumer<DOPNativePersonalizeRewardCubit, DOPNativePersonalizeRewardState>(
            listener: (BuildContext context, DOPNativePersonalizeRewardState state) {
              handleStateChanged(state);
            },
            builder: (BuildContext context, DOPNativePersonalizeRewardState state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  // Header section
                  _buildHeader(state),

                  // Expandable list
                  Expanded(
                    child: _buildRewardList(state),
                  ),

                  // CTA Button
                  _buildCTAButton(state),

                  // Bottom padding
                  const SizedBox(height: 20),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(DOPNativePersonalizeRewardState state) {
    String title = 'Chọn danh mục ưu đãi';
    String description = 'Chọn một danh mục để nhận ưu đãi phù hợp với nhu cầu của bạn';

    if (state is DOPNativePersonalizeRewardLoaded) {
      title = state.entity.header?.title ?? title;
      description = state.entity.header?.desc ?? description;
    }

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: dopNativeTextStyles.h400(
              color: dopNativeColors.textActive,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: dopNativeTextStyles.bodySmall(
              color: dopNativeColors.textPassive,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRewardList(DOPNativePersonalizeRewardState state) {
    final List<DOEPersonalizeRewardCategory> categories = <DOEPersonalizeRewardCategory>[];
    DOEPersonalizeRewardCategory? selectedItem;

    if (state is DOPNativePersonalizeRewardLoaded) {
      categories.addAll(state.entity.category ?? <DOEPersonalizeRewardCategory>[]);
    }

    if (state is DOPNativePersonalizeRewardSelected) {
      selectedItem = state.selectedItem;
    }

    return DOPNativePersonalizeRewardListWidget(
      categories: categories,
      selectedItem: selectedItem,
      onCategorySelected: cubit.onSelectReward,
    );
  }

  Widget _buildCTAButton(DOPNativePersonalizeRewardState state) {
    DOEPersonalizeRewardCategory? selectedItem;

    if (state is DOPNativePersonalizeRewardSelected) {
      selectedItem = state.selectedItem;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: CommonButton(
        onPressed: selectedIndex == null ? null : cubit.submitReward,
        style: dopNativeButtonStyles.primary(ButtonSize.medium),
        child: const Text(DOPNativeStrings.dopNativeNext),
      ),
    );
  }

  @visibleForTesting
  Widget buildCTAButton() {
    return BlocBuilder<DOPNativePersonalizeRewardCubit, DOPNativePersonalizeRewardState>(
      buildWhen: (_, DOPNativePersonalizeRewardState current) {
        return current is DOPNativePersonalizeRewardSelected;
      },
      builder: (BuildContext context, DOPNativePersonalizeRewardState state) {
        return _buildCTAButton(state);
      },
    );
  }

  @visibleForTesting
  void handleStateChanged(DOPNativePersonalizeRewardState state) {
    if (state is DOPNativePersonalizeRewardLoading) {
      showDOPLoading();
      return;
    }

    if (state is DOPNativePersonalizeRewardSubmitSuccess) {
      dopNativeApplicationStateCubit.getApplicationState();
      return;
    }

    hideDOPLoading();
    if (state is DOPNativePersonalizeRewardError) {
      handleDopEvoApiError(state.error);
      return;
    }
  }
}
