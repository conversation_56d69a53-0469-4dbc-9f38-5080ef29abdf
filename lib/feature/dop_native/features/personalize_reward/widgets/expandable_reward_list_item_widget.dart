// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_markdown_plus/flutter_markdown_plus.dart';

import '../../../../../resources/resources.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../widgets/radio_button/dop_native_radio_button_widget.dart';
import '../models/expandable_reward_list_item_model.dart';

class ExpandableRewardListItemWidget extends StatefulWidget {
  final ExpandableRewardListItemModel item;

  final VoidCallback? onTap;

  final VoidCallback? onSelect;

  const ExpandableRewardListItemWidget({
    required this.item,
    this.onTap,
    this.onSelect,
    super.key,
  });

  @override
  State<ExpandableRewardListItemWidget> createState() => _ExpandableRewardListItemWidgetState();
}

class _ExpandableRewardListItemWidgetState extends State<ExpandableRewardListItemWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  final Duration animationDuration = const Duration(milliseconds: 300);

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: animationDuration,
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Set initial state
    if (widget.item.isSelected) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(ExpandableRewardListItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Animate when expansion state changes
    if (oldWidget.item.isSelected != widget.item.isSelected) {
      if (widget.item.isSelected) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        color: dopNativeColors.background,
        borderRadius: BorderRadius.circular(12),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: <Widget>[
          _buildHeader(),
          _buildExpandableContent(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return InkWell(
      onTap: widget.onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: <Widget>[
            DOPNativeRadioButtonWidget(
              selected: widget.item.isSelected,
              onTap: widget.onSelect ?? () {},
            ),
            const SizedBox(width: 16),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    widget.item.title,
                    style: dopNativeTextStyles
                        .h300(
                          color: dopNativeColors.textActive,
                        )
                        .copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: 8),
                  _buildLogoRow(),
                ],
              ),
            ),

            // Expand/collapse icon
            AnimatedRotation(
              turns: widget.item.isSelected ? 0.5 : 0,
              duration: animationDuration,
              child: Icon(
                Icons.keyboard_arrow_down,
                color: dopNativeColors.textPassive,
                size: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoRow() {
    const int maxVisibleLogos = 6;
    final List<String> visibleLogos = widget.item.logoIcons.take(maxVisibleLogos).toList();
    final int remainingCount = widget.item.logoIcons.length - maxVisibleLogos;

    return Row(
      children: [
        ...visibleLogos.map((String logoPath) => _buildLogoIcon(logoPath)),
        if (remainingCount > 0) _buildMoreIndicator(remainingCount),
      ],
    );
  }

  Widget _buildLogoIcon(String logoPath) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: dopNativeColors.textFieldBorder,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: evoImageProvider.asset(logoPath),
      ),
    );
  }

  Widget _buildMoreIndicator(int count) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: dopNativeColors.textPassive.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          '+$count',
          style: dopNativeTextStyles
              .bodyXSmall(
                color: dopNativeColors.textPassive,
              )
              .copyWith(
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
        ),
      ),
    );
  }

  Widget _buildExpandableContent() {
    return SizeTransition(
      sizeFactor: _expandAnimation,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: dopNativeColors.background.withOpacity(0.5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: widget.item.shortDesc
                .map(
                  (String e) => MarkdownBody(
                    data: e,
                    fitContent: false,
                    styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
                      p: dopNativeTextStyles.bodyLarge(dopNativeColors.textPassive),
                      strong: dopNativeTextStyles.h300(color: dopNativeColors.textActive),
                      textAlign: WrapAlignment.center,
                    ),
                  ),
                )
                .toList(),
          ),
        ),
      ),
    );
  }
}
