import 'package:flutter/material.dart';

import '../../../../../data/response/dop_native/dop_native_personalize_reward_entity.dart';
import '../models/expandable_reward_list_item_model.dart';
import 'expandable_reward_list_item_widget.dart';

class DOPNativePersonalizeRewardListWidget extends StatefulWidget {
  /// List of reward categories from API
  final List<DOEPersonalizeRewardCategory> categories;

  /// Currently selected category index
  final DOEPersonalizeRewardCategory? selectedItem;

  /// Callback when a category is selected
  final void Function(ExpandableRewardListItemModel index)? onCategorySelected;

  const DOPNativePersonalizeRewardListWidget({
    required this.categories,
    this.selectedItem,
    this.onCategorySelected,
    super.key,
  });

  @override
  State<DOPNativePersonalizeRewardListWidget> createState() =>
      _DOPNativePersonalizeRewardListWidgetState();
}

class _DOPNativePersonalizeRewardListWidgetState
    extends State<DOPNativePersonalizeRewardListWidget> {
  String? expandedItemId;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: widget.categories.length,
      itemBuilder: (BuildContext context, int index) {
        final DOEPersonalizeRewardCategory item = widget.categories[index];
        final bool isSelected = widget.selectedItem == item;

        return ExpandableRewardListItemWidget(
          item: item,
          onTap: () => handleItemSelect(item.code),
        );
      },
    );
  }

  void handleItemSelected(DOEPersonalizeRewardCategory item) {
    widget.onCategorySelected?.call(item);
  }
}
