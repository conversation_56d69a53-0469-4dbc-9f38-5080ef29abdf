// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/response/dop_native/dop_native_personalize_reward_entity.dart';

class DOPNativePersonalizeRewardState implements BlocState {}

class DOPNativePersonalizeRewardInitial extends DOPNativePersonalizeRewardState {}

class DOPNativePersonalizeRewardLoading extends DOPNativePersonalizeRewardState {}

class DOPNativePersonalizeRewardLoaded extends DOPNativePersonalizeRewardState {
  final DOEPersonalizeRewardEntity entity;

  DOPNativePersonalizeRewardLoaded(this.entity);
}

class DOPNativePersonalizeRewardError extends DOPNativePersonalizeRewardState {
  final ErrorUIModel? error;

  DOPNativePersonalizeRewardError(this.error);
}

class DOPNativePersonalizeRewardSelected extends DOPNativePersonalizeRewardState {
  final DOEPersonalizeRewardCategory selectedItem;

  DOPNativePersonalizeRewardSelected(this.selectedItem);
}

class DOPNativePersonalizeRewardSubmitSuccess extends DOPNativePersonalizeRewardState {}
